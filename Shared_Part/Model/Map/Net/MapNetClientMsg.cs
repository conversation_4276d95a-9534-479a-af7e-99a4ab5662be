using MemoryPack;

namespace <PERSON><PERSON>ouJi
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientMoveInMsg)]
  public partial class ClientMoveInMsg : MaoYouMessage, ILocationMessage
  {
    public string targetMap;
    public string targetPoint;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientFastTransferMsg)]
  public partial class ClientFastTransferMsg : MaoYouMessage, ILocationMessage
  {
    public bool useFengZheng = false;
    public string transMap;
    public string transPoint;
    public long fengZhengId;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientTravelMapWithPlaneMsg)]
  public partial class ClientTravelMapWithPlaneMsg : MaoYouMessage, ILocationMessage
  {
    public string targetMap;
  }
}