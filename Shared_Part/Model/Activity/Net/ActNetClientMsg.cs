using MemoryPack;

namespace <PERSON><PERSON>ou<PERSON>i
{
  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientEntryDaToShaMsg)]
  public partial class ClientEntryDaToShaMsg : MaoYouInMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientExitDaToShaMsg)]
  public partial class ClientExitDaToShaMsg : MaoYouInMessage, IGlobalMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDaTaoShaCureMsg)]
  public partial class ClientDaTaoShaCureMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDaTaoShaLeiDaMsg)]
  public partial class ClientDaTaoShaLeiDaMsg : MaoYouInMessage, ILocationMessage
  {
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDaTaoAddAttrMsg)]
  public partial class ClientDaTaoAddAttrMsg : MaoYouInMessage, ILocationMessage
  {
    public int addType;
  }

  [MemoryPackable]
  [Message(MaoOuterMessageRange.ClientDaTaoCombDrugMsg)]
  public partial class ClientDaTaoCombDrugMsg : MaoYouInMessage, ILocationMessage
  {
    public int compType;
  }
}