using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientMoveInHandler : MessageLocationHandler<MapNode, ClientMoveInMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientMoveInMsg message)
    {
      // 检查用户是否在当前地图
      LogicRet getUserRet = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user.SendToast(getUserRet.Message);
        return;
      }
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      MapNode targetMapNode = GlobalInfoCache.Instance.GetMapNode(message.targetMap, message.targetPoint);
      if (targetMapNode == null)
      {
        ETLog.Error($"目标地图节点为空: {message.targetMap} {message.targetPoint}");
        user.SendToast("目标地图不存在");
        return;
      }
      LogicRet logicRet = moveComponent.CanMove(targetMapNode);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      logicRet = moveComponent.CanReach(targetMapNode);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      logicRet = moveComponent.MoveTo(targetMapNode, true);
      if (!logicRet.IsSuccess)
      {
        ETLog.Error($"移动失败: {logicRet.Message}");
        user.SendToast(logicRet.Message);
        return;
      }
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientFastTransferHandler : MessageLocationHandler<MapNode, ClientFastTransferMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientFastTransferMsg message)
    {
      // 检查用户是否在当前地图
      LogicRet getUserRet = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user.SendToast(getUserRet.Message);
        return;
      }

      // 获取目标地图节点
      MapNode targetMapNode = GlobalInfoCache.Instance.GetMapNode(message.transMap, message.transPoint);
      if (targetMapNode == null)
      {
        user.SendToast("目标地图不存在");
        return;
      }

      if (MapConstant.canNotUseFastTransferMap.Contains(nowMap.mapName))
      {
        user.SendToast("当前无法使用快捷传送！");
        return;
      }

      if (user.IsInVfxSystem())
      {
        user.SendToast("挂机中无法使用快捷传送！");
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Treasure yuMao = bagComponent.GetThingInBag<Treasure>(ThingNameEnum.JiYi_YuMao);
      if (yuMao == null)
      {
        user.SendToast("该地点不在保存地点中！");
        return;
      }

      // 检查目标地点是否在保存地点中
      bool find = false;
      List<PointInfo> allPoints = [.. yuMao.cityPoints, .. yuMao.outPoints];
      foreach (PointInfo pointInfo in allPoints)
      {
        if (pointInfo.mapName == message.transMap && pointInfo.pointName == message.transPoint)
        {
          find = true;
          break;
        }
      }
      if (!find)
      {
        user.SendToast("该地点不在保存地点中！");
        return;
      }

      if (!message.useFengZheng)
      {
        // 使用传送石
        Treasure chuansong = bagComponent.GetThingInBag<Treasure>(ThingNameEnum.ChuanSong_Shi);
        if (chuansong == null)
        {
          user.SendToast("您的背包中没有传送石！");
          return;
        }
        if (targetMapNode.nodeType != MapNodeType.CITY)
        {
          user.SendToast("传送地点不是城镇地区！");
          return;
        }
        if (chuansong.coolTime != 0 && chuansong.coolTime > TimeInfo.Instance.ServerNow())
        {
          user.SendToast("传送石还在冷却中！");
          return;
        }

        // 执行移动
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        LogicRet moveRet = moveComponent.MoveTo(targetMapNode, true);
        if (!moveRet.IsSuccess)
        {
          user.SendToast(moveRet.Message);
          return;
        }

        ETLog.Info($"fastTransfer: userId={user.Id}, useFengZheng={message.useFengZheng}, transMap={message.transMap}, transPoint={message.transPoint}, targetMap={targetMapNode.mapName}, targetPoint={targetMapNode.pointName}");

        chuansong.coolTime = TimeInfo.Instance.ServerNow() + chuansong.cd;
        ServerUpdateBagMsg updateBagMsg = new();
        updateBagMsg.updateList.Add(chuansong);
        user.SendMessage(updateBagMsg);
      }
      else
      {
        // 使用纸风筝
        Treasure fengzheng = bagComponent.GetThingInBag<Treasure>(message.fengZhengId);
        if (fengzheng == null || fengzheng.num < 1)
        {
          user.SendToast("背包中没有足够多的纸风筝！");
          return;
        }

        // 执行移动
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        LogicRet moveRet = moveComponent.MoveTo(targetMapNode, true);
        if (!moveRet.IsSuccess)
        {
          user.SendToast(moveRet.Message);
          return;
        }

        ETLog.Info($"fastTransfer: userId={user.Id}, useFengZheng={message.useFengZheng}, transMap={message.transMap}, transPoint={message.transPoint}, targetMap={targetMapNode.mapName}, targetPoint={targetMapNode.pointName}");

        bagComponent.AddThingNumWithSend(fengzheng, -1);
        TaskComponent taskComponent = user.GetComponent<TaskComponent>();
        taskComponent.FillTaskRequire(TaskRequireType.Use_Thing_Cond, ThingNameEnum.Zhi_FengZheng.ToString(), 1);
      }

      user.SendToast("传送成功！");
      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientTravelMapWithPlaneHandler : MessageLocationHandler<MapNode, ClientTravelMapWithPlaneMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientTravelMapWithPlaneMsg message)
    {
      // 检查用户是否在当前地图
      LogicRet getUserRet = nowMap.GetUserWithCheck(message.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user.SendToast(getUserRet.Message);
        return;
      }

      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      // 检查是否在驿站
      if (nowMap.nodeType != MapNodeType.CITY || nowMap.pointName != "驿站")
      {
        user.SendToast("当前不在驿站");
        return;
      }

      // 检查目标地图是否与当前地图相同
      if (nowMap.mapName == message.targetMap)
      {
        user.SendToast("当前地图与目标地图相同");
        return;
      }

      // 获取目标地图的驿站
      MapNode targetNode = GlobalInfoCache.Instance.GetMapNode(message.targetMap, "驿站");
      if (targetNode == null)
      {
        user.SendToast("该区域暂未开放");
        return;
      }

      // 检查等级和金币
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      if (attackComponent.level > 10)
      {
        if (bagComponent.coin < 100)
        {
          user.SendToast("您的金币不足");
          return;
        }
        bagComponent.coin -= 100;
        bagComponent.SendNewCoin();
      }

      // 执行移动
      LogicRet moveRet = moveComponent.MoveTo(targetNode, true);
      if (!moveRet.IsSuccess)
      {
        user.SendToast(moveRet.Message);
        return;
      }

      // 发送聊天消息到原地图
      nowMap.SendMessageToMapUser(new ServerSendChatMsg(
        $"专用空姐挽着<u>{user.nickname}</u>,登上波音气死妻,一路欢声笑语的往<u>{targetNode.mapName}</u>去了.",
        ChatType.Local_Chat), user.Id);

      // 发送聊天消息到目标地图
      targetNode.SendMessagesToMapUser(new ServerSendChatMsg(
        $"来自<u>{nowMap.mapName}</u>的波音气死妻号飞机落地，专用空姐挽着<u>{user.nickname}</u>，载歌载舞地走了下来",
        ChatType.Local_Chat));

      user.SendToast("搭乘飞机成功");
      await ETTask.CompletedTask;
    }
  }
}