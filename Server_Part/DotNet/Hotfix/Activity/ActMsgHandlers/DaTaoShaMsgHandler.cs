using System;
using System.Collections.Generic;

namespace MaoYouJi
{
  [MessageHandler(SceneType.Global)]
  public class ClientEntryDaToShaMsgHandler : MessageHandler<Entity, ClientEntryDaToShaMsg>
  {
    protected override async ETTask Run(Entity entity, ClientEntryDaToShaMsg msg)
    {
      using (await entity.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DaTaoSha, msg.UserId))
      {
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user);
        if (logicRet != LogicRet.Success)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
        DaTaoShaActComp daTaoShaActComp = entity.Root().GetComponent<DaTaoShaActComp>();
        logicRet = daTaoShaActComp.CanEnterDaTaoSha(user);
        if (logicRet != LogicRet.Success)
        {
          user.SendToast(logicRet.Message);
          return;
        }
        // 随机移动到岛上的一个点
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        List<string> dataShaPoints = GlobalInfoCache.Instance.mapPoints[MapNameConstant.DaTaoShaDao];
        string randomPoint = RandomGenerator.RandomArray(dataShaPoints);
        InnerMoveUserReq outMoveUserReq = new InnerMoveUserReq
        {
          TargetUserId = msg.UserId,
          OutNowMapName = moveComponent.nowMap,
          OutNowPointName = moveComponent.nowPoint,
          TargetMapName = MapNameConstant.DaTaoShaDao,
          TargetPointName = randomPoint,
          IsForceMove = false
        };
        InnerMoveUserResp outMoveUserResp = await entity.Root().GetComponent<MessageSender>().Call(user.GetParent<MapNode>().GetActorId(), outMoveUserReq) as InnerMoveUserResp;
        if (outMoveUserResp.Error != 0)
        {
          ETLog.Error($"进入大逃杀活动失败: {outMoveUserResp.Error} {outMoveUserResp.showMessage}");
          user.SendToast("进入大逃杀活动失败");
          return;
        }
        user.AddComponent<UserDaTaoShaInfoComp, DaTaoShaActComp>(daTaoShaActComp);
      }
    }
  }

  [MessageHandler(SceneType.Global)]
  public class ClientExitDaToShaMsgHandler : MessageHandler<Entity, ClientExitDaToShaMsg>
  {
    protected override async ETTask Run(Entity entity, ClientExitDaToShaMsg msg)
    {
      using (await entity.Root().GetComponent<CoroutineLockComponent>().Wait(CoroutineLockType.DaTaoSha, msg.UserId))
      {
        LogicRet logicRet = UserProcSystem.GetUserWithCheck(msg.UserId, out User user, true);
        if (logicRet != LogicRet.Success)
        {
          user?.SendToast(logicRet.Message);
          return;
        }
        DaTaoShaActComp daTaoShaActComp = entity.Root().GetComponent<DaTaoShaActComp>();
        logicRet = daTaoShaActComp.CanExitDaTaoSha(user);
        if (logicRet != LogicRet.Success)
        {
          user.SendToast(logicRet.Message);
          return;
        }

        string targetMapName = MapNameConstant.MaoYinCun;
        string targetPointName = "猫隐村广场";

        // 移动用户离开大逃杀岛
        MoveComponent moveComponent = user.GetComponent<MoveComponent>();
        InnerMoveUserReq outMoveUserReq = new InnerMoveUserReq
        {
          TargetUserId = msg.UserId,
          OutNowMapName = moveComponent.nowMap,
          OutNowPointName = moveComponent.nowPoint,
          TargetMapName = targetMapName,
          TargetPointName = targetPointName,
          IsForceMove = false // 强制移动
        };
        InnerMoveUserResp outMoveUserResp = await entity.Root().GetComponent<MessageSender>().Call(user.GetParent<MapNode>().GetActorId(), outMoveUserReq) as InnerMoveUserResp;
        if (outMoveUserResp.Error != 0)
        {
          ETLog.Error($"离开大逃杀活动失败: {outMoveUserResp.Error} {outMoveUserResp.showMessage}");
          user.SendToast("离开大逃杀活动失败");
          return;
        }

        // 恢复用户数据
        daTaoShaActComp.QuitDaTaoSha(user);

        int count = daTaoShaActComp.DaTaoShaUserList.Count;
        ETLog.Info($"离开大逃杀活动: {msg.UserId} {count}");
        ChatProSystem.SendMessageToAllUser(new ServerSendChatMsg
        {
          content = $"【大逃杀】 <u>{user.nickname}</u> 离开了大逃杀岛({count})！",
          chatType = ChatType.World_Chat
        });

        user.SendToast("成功离开大逃杀活动");
      }
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientDaTaoShaCureMsgHandler : MessageLocationHandler<MapNode, ClientDaTaoShaCureMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDaTaoShaCureMsg msg)
    {
      LogicRet getUserRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!getUserRet.IsSuccess)
      {
        user?.SendToast(getUserRet.Message);
        return;
      }
      BagComponent bagSystem = user.GetComponent<BagComponent>();
      Thing daTaoShaKuangShi = bagSystem.GetThingInBag<Thing>(ThingNameEnum.DaTaoSha_KuangShi);
      if (daTaoShaKuangShi == null)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      if (daTaoShaKuangShi.num < 3)
      {
        user.SendToast("背包中的矿石不足");
        return;
      }
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.blue = attackComponent.maxBlue;
      bagSystem.AddThingNumWithSend(daTaoShaKuangShi, -3);
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      user.SendToast("治疗成功，消耗3个矿石！");
      await ETTask.CompletedTask;
    }
  }

@NeedLogin
  @ActBizType(ActBizEnum.DaTaoSha_Add_Attr)
  public CommonOutParams daTaoShaAddAttr(DaTaoShaAddAttrIn inParams) throws Exception
    {
      ComUtil.lockReq(ThreadUtil.getUser().id + "_daTaoShaAddAttr");
      User user = ThreadUtil.getUser();
      BagSystem bagSystem = ThreadUtil.getBagSystem();
    if (user.checkLiveState(LiveStateEnum.FIGHTING, LiveStateEnum.DEAD)) {
        return new CommonOutParams(false, "您当前无法增加属性哦！");
      }
      Thing daTaoShaKuangShi = bagSystem.GetThingInBag(ThingNameEnum.DaTaoSha_KuangShi, null, null);
    if (daTaoShaKuangShi == null) {
        return new CommonOutParams(false, "背包中的矿石不足");
      }
    if (daTaoShaKuangShi.num < 1) {
        return new CommonOutParams(false, "背包中的矿石不足");
      }
      bagSystem.addThingNumWithSend(daTaoShaKuangShi, -1);
      DaTaoShaActConf.DaTaoShaUserInfo daTaoShaUserInfo = activityProc.getDaTaoShaUserInfo(user.id);
    if (inParams.addType == 1) {
        daTaoShaUserInfo.addPower += 1;
      } else if (inParams.addType == 2) {
        daTaoShaUserInfo.addQuick += 1;
      } else if (inParams.addType == 3) {
        daTaoShaUserInfo.addStrength += 1;
      } else if (inParams.addType == 4) {
        daTaoShaUserInfo.addIq += 1;
      } else if (inParams.addType == 5) {
        daTaoShaUserInfo.addMind += 1;
      }
      userBaseProc.recalUserAttrs(user);
      SocketUtil.sendMessageToUser(user.id, new UpdatePartUserInfoOut(user, UserUpdateFlagEnum.Base_Attack));
    return new ShowToastOut("属性增加成功！");
  }

  @NeedLogin
  @LockReq
  @ActBizType(ActBizEnum.DaTaoSha_Comp_Drug)
  public CommonOutParams daTaoShaCompDrug(DaTaoShaCompDrugIn inParams) throws Exception
{
  BagSystem bagSystem = ThreadUtil.getBagSystem();
  User user = ThreadUtil.getUser();
    if (user.activityName != ActNameEnum.Da_TaoSha) {
    return new CommonOutParams(false, "您当前没有参与大逃杀活动哦！");
  }
  ThingNameEnum thingNameEnum1 = ThingNameEnum.DaTaoSha_XiaoXueJi, thingNameEnum2 = ThingNameEnum.DaTaoSha_ZhongXueJi;
    if (inParams.compType == 2) {
    thingNameEnum1 = ThingNameEnum.DaTaoSha_ZhongXueJi;
    thingNameEnum2 = ThingNameEnum.DaTaoSha_DaXueJi;
  } else if (inParams.compType == 3) {
    thingNameEnum1 = ThingNameEnum.DaTaoSha_XiaoLanJi;
    thingNameEnum2 = ThingNameEnum.DaTaoSha_ZhongLanJi;
  } else if (inParams.compType == 4) {
    thingNameEnum1 = ThingNameEnum.DaTaoSha_ZhongLanJi;
    thingNameEnum2 = ThingNameEnum.DaTaoSha_DaLanJi;
  }
  Thing thing1 = bagSystem.GetThingInBag(thingNameEnum1, null, null);
    if (thing1 == null || thing1.num < 3) {
    return new CommonOutParams(false, "背包中的" + thingNameEnum1.getName() + "不足");
  }
  bagSystem.addThingNumWithSend(thing1, -3);
  ThingGiveInfo thingGiveInfo = new ThingGiveInfo(thingNameEnum2, OwnType.PRIVATE, 1);
bagProc.giveThing(bagSystem, thingGiveInfo);
return new ShowToastOut("合成成功！");
  }

  @NeedLogin
  @ActBizType(ActBizEnum.DaTaoSha_LeiDa)
  public CommonOutParams daTaoShaLeiDa(CommonInParams inParams) throws Exception
{
  ComUtil.lockReq(ThreadUtil.getUser().id + "_daTaoShaLeiDa");
  BagSystem bagSystem = ThreadUtil.getBagSystem();
  User user = ThreadUtil.getUser();
    if (user.activityName != ActNameEnum.Da_TaoSha) {
    return new CommonOutParams(false, "您当前没有参与大逃杀活动哦！");
  }
  Thing daTaoShaKuangShi = bagSystem.GetThingInBag(ThingNameEnum.DaTaoSha_KuangShi, null, null);
    if (daTaoShaKuangShi == null) {
    return new CommonOutParams(false, "背包中的矿石不足");
  }
    if (daTaoShaKuangShi.num < 1) {
    return new CommonOutParams(false, "背包中的矿石不足");
  }
  DaTaoShaActConf daTaoShaAct = activityProc.getConfig().daTaoShaAct;
  daTaoShaAct.lock(false);
    try {
    Set<String> userIds = new HashSet<>(daTaoShaAct.nowUsers.keySet());
    Set<String> showUserIds = new HashSet<>();
    userIds.remove(user.id);
    if (userIds.size() > 5)
    {
      showUserIds.addAll(userIds.stream().limit(5).collect(Collectors.toList()));
    }
    else
    {
      showUserIds.addAll(userIds);
    }
    if (showUserIds.size() == 0)
    {
      return new ShowToastOut("当前没有其他玩家哦！");
    }
    SendChatOut sendChatOut = new SendChatOut();
    sendChatOut.chatType = ChatType.Sys_Chat;
    sendChatOut.content = "【大逃杀】 雷达扫描结果，当前岛上的玩家位置是：";
    for (String userId : showUserIds)
    {
      User otherUser = userBaseProc.getCacheUserById(userId);
      sendChatOut.content += " " + chatProc.getUserName(otherUser.name) + "在<u>"
          + otherUser.nowPoint + "</u>,";
    }
    SocketUtil.sendMessageToUser(user.id, sendChatOut);
  } finally {
    daTaoShaAct.unlock(false);
  }
  bagSystem.addThingNumWithSend(daTaoShaKuangShi, -1);
    return new ShowToastOut("玩家位置已经显示在系统聊天栏中");
  }

}