using System;

namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ServerNpcTalkHandler : MessageLocationHandler<MapNode, ServerNpcTalkListReq, ServerNpcTalkListResp>
  {
    protected override async ETTask Run(MapNode nowMap, ServerNpcTalkListReq request, ServerNpcTalkListResp response)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(request.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        response.SetError(logicRet.Message);
        return;
      }
      if (!nowMap.ContainsNpc(request.NpcName))
      {
        response.SetError("NPC不在当前地图");
        return;
      }
      ServerNpcProcStruct serverNpcProcStruct = new ServerNpcProcStruct
      {
        ServerNpcTalkListResp = response,
        User = user,
        MapNode = nowMap
      };
      if (EventSystem.Instance.TryGetInvoke<ServerNpcProcStruct, ETTask>((long)request.NpcName, out var invokeHandler))
      {
        try
        {
          await invokeHandler.Handle(serverNpcProcStruct);
        }
        catch (Exception e)
        {
          ETLog.Error(e);
          response.SetError("服务器内部错误");
        }
      }
      else
      {
        response.SetError("服务器内部错误");
      }
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ServerNpcOptionHandler : MessageLocationHandler<MapNode, ClientNpcOptionMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientNpcOptionMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true);
      if (!logicRet.IsSuccess)
      {
        return;
      }
      EventSystem.Instance.TryGetInvoke<NpcOptionProcStruct, ETTask>((long)msg.talkOption, out var invokeHandler);
      if (invokeHandler == null)
      {
        ETLog.Error($"未找到Npc选项处理函数: {msg.talkOption}");
        user?.SendToast("服务器内部错误");
        return;
      }
      NpcOptionProcStruct npcOptionProcStruct = new NpcOptionProcStruct
      {
        ClientNpcOptionMsg = msg,
        User = user,
        MapNode = nowMap
      };
      try
      {
        await invokeHandler.Handle(npcOptionProcStruct);
      }
      catch (Exception e)
      {
        ETLog.Error(e);
        user?.SendToast("服务器内部错误");
      }
    }
  }


  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientRequestCureHandler : MessageLocationHandler<MapNode, ClientRequestCureMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientRequestCureMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      UserTimeInfo userTimeInfo = user.GetComponent<UserTimeInfo>();
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      // 检查是否需要治疗
      if (attackComponent.blood == attackComponent.maxBlood && attackComponent.blue == attackComponent.maxBlue)
      {
        user.SendToast("您的宠物状态已满，无需治疗！");
        return;
      }

      long costNum = attackComponent.maxBlood - attackComponent.blood + attackComponent.maxBlue - attackComponent.blue;
      costNum /= 20;
      if (costNum <= 0)
      {
        costNum = 0;
      }

      // 检查地图和冷却时间
      if (moveComponent.nowMap != "幼稚园")
      {
        long remainTime = userTimeInfo.lastCureTime + 1000 * 60 - TimeInfo.Instance.ServerNow();
        if (remainTime > 0)
        {
          user.SendToast("治疗冷却中！剩余时间：" + remainTime / 1000 + "秒");
          return;
        }
      }
      else
      {
        costNum = 0;
      }

      // 检查费用
      if (costNum > 0)
      {
        if (bagComponent.coin < costNum)
        {
          user.SendToast("您的钱币不足以治疗！");
          return;
        }
        bagComponent.AddAllCoinWithSend(-costNum);
      }

      // 执行治疗
      userTimeInfo.lastCureTime = TimeInfo.Instance.ServerNow();
      attackComponent.blood = attackComponent.maxBlood;
      attackComponent.blue = attackComponent.maxBlue;

      // 发送消息
      user.SendChat("您的宠物回满了HP和SP！", ChatType.Sys_Chat);
      user.SendMessage(ServerUpdatePartUserInfoMsg.Create(user, UserUpdateFlagEnum.Blood, UserUpdateFlagEnum.Blue));
      user.SendToast("治疗成功，您的宠物状态已满！");

      await ETTask.CompletedTask;
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class ClientComHunPoHandler : MessageLocationHandler<MapNode, ClientComHunPoMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientComHunPoMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user, true, true);
      if (!logicRet.IsSuccess)
      {
        user?.SendToast(logicRet.Message);
        return;
      }

      BagComponent bagComponent = user.GetComponent<BagComponent>();

      // 检查输入参数
      if (msg.destThing == ThingNameEnum.None || msg.srcThing == ThingNameEnum.None)
      {
        user.SendToast("输入为空");
        return;
      }

      // 检查魂魄类型是否匹配
      string[] descHunPoStr = msg.destThing.ToString().Split('_');
      string[] srcHunPoStr = msg.srcThing.ToString().Split('_');
      if (descHunPoStr.Length < 2 || srcHunPoStr.Length < 2 ||
          descHunPoStr[1] != srcHunPoStr[1] || descHunPoStr[1] != "HunPo")
      {
        user.SendToast("输入错误");
        return;
      }

      // 检查背包中的源魂魄
      Thing srcThing = bagComponent.GetThingInBag<Thing>(msg.srcThing);
      if (srcThing == null || srcThing.num < 10)
      {
        user.SendToast("背包中没有足够的" + EnumDescriptionCache.GetDescription(msg.srcThing));
        return;
      }

      // 消耗源魂魄
      bagComponent.AddThingNumWithSend(srcThing, -10);

      // 给予目标魂魄
      ThingGiveInfo giveInfo = new(msg.destThing, OwnType.PUBLIC, 1);
      bagComponent.GiveThing(giveInfo);

      user.SendToast("合成魂魄成功！");
      await ETTask.CompletedTask;
    }
  }
}
