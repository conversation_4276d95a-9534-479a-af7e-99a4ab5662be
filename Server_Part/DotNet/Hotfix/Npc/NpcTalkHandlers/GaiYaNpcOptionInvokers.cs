namespace MaoYouJi
{
  [Invoke((long)NpcTalkOptionEnum.server_enter_magic_space)]
  public class ServerEnterMagicSpaceHandler : AInvokeHandler<NpcOptionProcStruct, ETTask>
  {
    public override async ETTask Handle(NpcOptionProcStruct data)
    {
      User user = data.User;
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();

      if (moveComponent.nowMap != MapNameConstant.GaiYaZhiCheng || moveComponent.nowPoint != "魔导师学院")
      {
        user.SendToast("您的位置不正确，无法进入魔法空间");
        return;
      }
      LogicRet logicRet = moveComponent.CanMove();
      if (logicRet != LogicRet.Success)
      {
        user.SendToast(logicRet.Message);
        return;
      }

      moveComponent.MoveTo(MapNameConstant.MoFaKongJian, "魔法空间");
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      outParams.talkList = "close";
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcTalkOptionEnum.server_quit_magic_space)]
  public class ServerQuitMagicSpaceHandler : AInvokeHandler<NpcOptionProcStruct, ETTask>
  {
    public override async ETTask Handle(NpcOptionProcStruct data)
    {
      User user = data.User;
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();

      if (moveComponent.nowMap != MapNameConstant.MoFaKongJian || moveComponent.nowPoint != "魔法空间")
      {
        user.SendToast("您的位置不正确，无法退出魔法空间");
        return;
      }

      LogicRet logicRet = moveComponent.CanMove();
      if (logicRet != LogicRet.Success)
      {
        user.SendToast(logicRet.Message);
        return;
      }

      moveComponent.MoveTo(MapNameConstant.GaiYaZhiCheng, "魔导师学院");
      ServerNpcOptionMsg outParams = new ServerNpcOptionMsg();
      outParams.talkList = "close";
      user.SendMessage(outParams);
      await ETTask.CompletedTask;
    }
  }
}