namespace MaoYouJi
{
  [Invoke((long)NpcTalkOptionEnum.require_qiuka_task)]
  public class RequireQiukaTaskHandler : AInvokeHandler<NpcOptionProcStruct, ETTask>
  {
    public override async ETTask Handle(NpcOptionProcStruct data)
    {
      User user = data.User;
      MapNode mapNode = data.MapNode;
      TaskComponent taskComponent = user.GetComponent<TaskComponent>();

      BaseTask task = taskComponent.GetDoingTask(TaskIdEnum.Main_MiMi_QianRu1_35);
      if (task == null)
      {
        user.SendToast("您还没有接取俅卡任务！");
        return;
      }

      // 检查任务是否已完成
      if (task.requireLists.Count > 0 && task.requireLists[0].nowNum > 0)
      {
        user.SendToast("您已经完成俅卡任务了！");
        return;
      }

      AttackComponent userAttack = user.GetComponent<AttackComponent>();
      if (userAttack.LiveState != LiveStateEnum.ALIVE)
      {
        user.SendToast("当前状态无法进行该操作！");
        return;
      }

      // 生成俅卡分身怪物
      GenMonComponent genMonComponent = mapNode.GetComponent<GenMonComponent>();
      MonsterInfo monsterInfo = genMonComponent.GenOneNormalMon(MonBaseType.QiuKa_FenShen, user);
      if (monsterInfo == null)
      {
        user.SendToast("生成怪物失败！");
        return;
      }

      // 发起战斗
      MapAttackManage mapAttackManage = mapNode.GetComponent<MapAttackManage>();
      AttackComponent monsterAttack = monsterInfo.GetComponent<AttackComponent>();

      LogicRet attackResult = await mapAttackManage.StartAttackBase(monsterAttack, userAttack);
      if (!attackResult.IsSuccess)
      {
        user.SendToast(attackResult.Message);
        return;
      }

      await ETTask.CompletedTask;
    }
  }
}